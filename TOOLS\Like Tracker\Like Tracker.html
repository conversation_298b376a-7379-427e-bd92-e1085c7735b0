<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Like Tracker</title>

    <!-- Vue.js CDN -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.prod.js"></script>

    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: rgba(15, 15, 18, 1);
            color: white;
            overflow: hidden;
        }

        .app-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            gap: 10px;
            padding: 10px;
            max-width: 800px;
            margin: 0 auto;
        }

        .panel {
            background-color: rgba(32, 28, 28, 1);
            border-radius: 12px;
            padding: 15px;
        }

        /* Visual Ranking Display */
        .ranking-display {
            background: transparent;
            padding: 20px 20px 30px 20px; /* Extra bottom padding */
            border-radius: 12px;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.5s ease, visibility 0.5s ease;
            margin-bottom: 20px; /* Extra margin at bottom */
        }

        .ranking-display.visible {
            opacity: 1;
            visibility: visible;
        }

        .ranking-container {
            position: relative;
            min-height: 220px; /* Reserve space for 3 bars with no cutoff */
            max-height: 220px; /* Prevent expansion beyond 3 bars */
            overflow: hidden;
        }

        .ranking-bar {
            background-color: rgba(0, 0, 0, 0.8);
            border-radius: 25px;
            padding: 15px 20px;
            margin: 8px 0;
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 16px;
            font-weight: bold;
            color: white;
            min-height: 20px;
            max-height: 50px; /* Prevent expansion from larger profile pics */
            width: 50%; /* Make bars 50% shorter */
            transform: translateX(-100%);
            opacity: 0;
            transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            box-sizing: border-box; /* Ensure consistent sizing */
        }

        .ranking-bar.visible {
            transform: translateX(0);
            opacity: 1;
        }

        .ranking-bar.hidden {
            transform: translateX(-100%);
            opacity: 0;
        }

        .ranking-bar.swapping {
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .ranking-bar:hover {
            background-color: rgba(0, 0, 0, 0.9);
            transform: translateX(5px);
        }

        .ranking-bar.slide-in:nth-child(1) {
            animation-delay: 0.1s;
        }

        .ranking-bar.slide-in:nth-child(2) {
            animation-delay: 0.3s;
        }

        .ranking-bar.slide-in:nth-child(3) {
            animation-delay: 0.5s;
        }

        /* Keyframe animations for better control */
        @keyframes slideInFromLeft {
            from {
                transform: translateX(-100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOutToLeft {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(-100%);
                opacity: 0;
            }
        }

        .ranking-bar.animate-in {
            animation: slideInFromLeft 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
        }

        .ranking-bar.animate-out {
            animation: slideOutToLeft 0.4s cubic-bezier(0.55, 0.055, 0.675, 0.19) forwards;
        }

        /* Vue transition-group animations */
        .ranking-enter-active {
            animation: slideInFromLeft 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .ranking-leave-active {
            animation: slideOutToLeft 0.3s cubic-bezier(0.55, 0.055, 0.675, 0.19);
            position: absolute;
            width: 50%;
        }

        .ranking-leave-to {
            opacity: 0;
            transform: translateX(-100%);
        }

        .ranking-move {
            transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        /* Ensure only 3 slots maximum */
        .ranking-display .ranking-bar:nth-child(n+4) {
            display: none !important;
        }

        .rank-profile {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
            flex-shrink: 0;
            transition: background-color 0.3s ease;
            overflow: hidden;
            border: 2px solid rgba(255, 255, 255, 0.3);
            position: relative;
            top: -5px; /* Offset to prevent bar expansion */
        }

        .rank-profile img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
        }

        .rank-profile.fallback {
            background-color: rgba(64, 64, 64, 0.8);
            color: white;
        }

        .user-name-display {
            flex: 1;
            text-align: left;
            font-size: 20px; /* Increased from 16px to 20px (+4) */
        }

        .like-count {
            background-color: rgba(64, 64, 64, 0.8);
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .heart-icon {
            width: 16px;
            height: 16px;
            flex-shrink: 0;
        }

        .connection-status {
            position: absolute;
            top: 10px;
            right: 10px;
            font-size: 12px;
            opacity: 0.7;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: background-color 0.2s;
        }

        .btn-secondary {
            background-color: rgba(64, 64, 64, 1);
            color: white;
        }

        .btn-secondary:hover {
            background-color: rgba(80, 80, 80, 1);
        }
    </style>
</head>
<body>
    <div id="app" class="app-container">
        <!-- Control Panel -->
        <div class="panel">
            <h3 style="margin-top: 0;">Like Tracker Control</h3>
            <div style="display: flex; gap: 8px; margin-top: 10px;">
                <button @click="resetLikes" class="btn btn-secondary" style="flex: 1;">
                    Reset All Likes
                </button>
            </div>
        </div>

        <!-- Visual Ranking Display -->
        <div class="ranking-display" :class="{ visible: hasAnyLikes }">
            <div class="ranking-container">
                <transition-group name="ranking" tag="div" mode="out-in">
                    <div
                        v-for="(user, index) in displayedUsers"
                        :key="user.userId"
                        class="ranking-bar visible"
                        :class="{
                            'swapping': isSwapping
                        }"
                        :style="{
                            transitionDelay: (index * 0.1) + 's',
                            zIndex: 100 - index
                        }"
                        :data-user-id="user.userId"
                        v-show="index < 3"
                    >
                        <div class="rank-profile" :class="{ fallback: !user.profilePictureUrl }">
                            <img v-if="user.profilePictureUrl" :src="user.profilePictureUrl" :alt="user.nickname" @error="handleImageError">
                            <span v-else>{{ index + 1 }}</span>
                        </div>
                        <div class="user-name-display">{{ user.nickname }}</div>
                        <div class="like-count">
                            {{ user.likes }}
                            <svg class="heart-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z" fill="#ff4444"/>
                            </svg>
                        </div>
                    </div>
                </transition-group>
            </div>
        </div>

        <!-- Connection Status -->
        <div class="connection-status">
            Status:
            <span :style="{ color: status === 'Connected' ? '#10b981' : status === 'Disconnected' ? '#ef4444' : '#f59e0b' }">
                {{ status }}
            </span>
        </div>
    </div>

    <script>
        const { createApp } = Vue;

        const app = createApp({
            data() {
                return {
                    status: 'Disconnected',
                    users: [], // Track users and their like counts
                    websocket: null,
                    isSwapping: false, // Track when positions are swapping
                    previousOrder: [] // Track previous user order for animations
                };
            },
            computed: {
                sortedUsers() {
                    // Only include users with likes > 0, then sort by likes
                    const usersWithLikes = this.users.filter(user => user.likes > 0);
                    const sorted = usersWithLikes.sort((a, b) => b.likes - a.likes);

                    // Check if order changed for animation
                    const currentOrder = sorted.map(u => u.userId);
                    if (this.previousOrder.length > 0 &&
                        JSON.stringify(currentOrder) !== JSON.stringify(this.previousOrder)) {
                        this.triggerSwapAnimation();
                    }
                    this.previousOrder = currentOrder;

                    return sorted;
                },
                hasAnyLikes() {
                    return this.users.some(user => user.likes > 0);
                },
                displayedUsers() {
                    // Get users with likes > 0, sorted by likes
                    const usersWithLikes = this.users
                        .filter(user => user.likes > 0)
                        .sort((a, b) => b.likes - a.likes);

                    // Strictly limit to top 3 only
                    const topThree = usersWithLikes.slice(0, 3);

                    // Return exactly 3 or fewer users
                    return topThree.map((user, index) => ({
                        ...user,
                        isVisible: true,
                        rank: index + 1
                    }));
                }
            },
            mounted() {
                this.setupWebSocket();
            },
            watch: {
                sortedUsers: {
                    handler(newUsers, oldUsers) {
                        // Trigger animations when rankings change
                        if (oldUsers && oldUsers.length > 0) {
                            this.handleRankingChange(newUsers, oldUsers);
                        }
                    },
                    deep: true
                }
            },
            methods: {
                triggerSwapAnimation() {
                    // Trigger swap animation
                    this.isSwapping = true;
                    setTimeout(() => {
                        this.isSwapping = false;
                    }, 400); // Match the CSS transition duration
                },

                handleRankingChange(newUsers, oldUsers) {
                    // Check if any users entered or left the top 3
                    const newIds = newUsers.map(u => u.userId);
                    const oldIds = oldUsers.map(u => u.userId);

                    const entered = newIds.filter(id => !oldIds.includes(id));
                    const left = oldIds.filter(id => !newIds.includes(id));

                    if (entered.length > 0 || left.length > 0) {
                        // Force re-render with animation
                        this.$nextTick(() => {
                            this.triggerSwapAnimation();
                        });
                    }
                },

                handleImageError(event) {
                    // Hide broken image and show fallback
                    event.target.style.display = 'none';
                    event.target.parentElement.classList.add('fallback');
                    const rank = Array.from(event.target.parentElement.parentElement.parentElement.children).indexOf(event.target.parentElement.parentElement) + 1;
                    event.target.parentElement.innerHTML = `<span>${rank}</span>`;
                },
                
                resetLikes() {
                    // Reset all like counts
                    this.users.forEach(user => {
                        user.likes = 0;
                    });
                    // Remove users with 0 likes from display
                    this.users = this.users.filter(user => user.likes > 0);
                },
                
                handleLikeMessage(data) {
                    if (data.event === 'like' && data.data && data.data.uniqueId && data.data.likeCount) {
                        const userId = data.data.userId || data.data.uniqueId;
                        const nickname = data.data.nickname || data.data.uniqueId;
                        const profilePictureUrl = data.data.profilePictureUrl || null;
                        const likeCount = data.data.likeCount || 1;
                        
                        this.processLike(userId, nickname, profilePictureUrl, likeCount);
                    }
                },
                
                processLike(userId, nickname, profilePictureUrl, likeCount) {
                    // Find existing user or create new one
                    let user = this.users.find(u => u.userId === userId);
                    
                    if (!user) {
                        // Create new user
                        user = {
                            userId: userId,
                            nickname: nickname,
                            profilePictureUrl: profilePictureUrl,
                            likes: 0
                        };
                        this.users.push(user);
                    }
                    
                    // Add likes to user
                    user.likes += likeCount;
                    
                    // Update user info in case it changed
                    user.nickname = nickname;
                    if (profilePictureUrl) {
                        user.profilePictureUrl = profilePictureUrl;
                    }
                },
                
                updateStatus(newStatus) {
                    this.status = newStatus;
                },
                
                setupWebSocket() {
                    this.connect();
                },
                
                connect() {
                    if (this.websocket) return;

                    this.websocket = new WebSocket("ws://localhost:21213/");

                    this.websocket.onopen = () => {
                        this.updateStatus("Connected");
                    };

                    this.websocket.onclose = () => {
                        this.updateStatus("Disconnected");
                        this.websocket = null;
                        setTimeout(() => this.connect(), 1000);
                    };

                    this.websocket.onerror = () => {
                        this.updateStatus("Connection Failed");
                        this.websocket = null;
                        setTimeout(() => this.connect(), 1000);
                    };

                    this.websocket.onmessage = (event) => {
                        let parsedData = JSON.parse(event.data);
                        console.log("Data received", parsedData);
                        this.handleLikeMessage(parsedData);
                    };
                }
            }
        }).mount('#app');
    </script>
</body>
</html>
