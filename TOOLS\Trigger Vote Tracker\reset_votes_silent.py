#!/usr/bin/env python3
"""
Silent Reset Command for Stream Deck
Sends reset command via TikFinity WebSocket with no output/windows
"""

import asyncio
import websockets
import json
import sys
import os

async def send_reset_command():
    """Send reset command silently"""
    try:
        # Connect to TikFinity WebSocket
        uri = "ws://localhost:21213/"
        async with websockets.connect(uri, ping_timeout=2, close_timeout=2) as websocket:
            # Send custom command
            command_data = {
                "event": "custom_command",
                "command": "reset_votes"
            }
            
            await websocket.send(json.dumps(command_data))
            # Brief wait to ensure delivery
            await asyncio.sleep(0.1)
            
    except:
        # Fail silently - no error messages for Stream Deck
        pass

if __name__ == "__main__":
    # Suppress all output for silent operation
    if os.name == 'nt':  # Windows
        import subprocess
        import sys
        
        # Hide console window
        startupinfo = subprocess.STARTUPINFO()
        startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
        startupinfo.wShowWindow = subprocess.SW_HIDE
    
    # Run the command
    try:
        asyncio.run(send_reset_command())
    except:
        # Fail silently
        pass
