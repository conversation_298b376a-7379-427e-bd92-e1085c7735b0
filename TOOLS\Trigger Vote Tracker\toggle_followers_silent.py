#!/usr/bin/env python3
"""
Silent Toggle Followers Command for Stream Deck
"""

import asyncio
import websockets
import json
import sys
import os

async def send_toggle_command():
    """Send toggle followers command silently"""
    try:
        uri = "ws://localhost:21213/"
        async with websockets.connect(uri, ping_timeout=2, close_timeout=2) as websocket:
            command_data = {
                "event": "custom_command",
                "command": "toggle_followers_only"
            }
            
            await websocket.send(json.dumps(command_data))
            await asyncio.sleep(0.1)
            
    except:
        pass

if __name__ == "__main__":
    try:
        asyncio.run(send_toggle_command())
    except:
        pass
