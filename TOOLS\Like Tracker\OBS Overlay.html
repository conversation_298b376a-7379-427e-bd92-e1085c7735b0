<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Like Tracker - OBS Overlay</title>

    <!-- Vue.js CDN -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.prod.js"></script>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: transparent;
            overflow: hidden;
            width: 100vw;
            height: 100vh;
        }

        .overlay-container {
            position: absolute;
            top: 20px;
            left: 20px;
            width: auto;
            height: auto;
            pointer-events: none; /* Allow clicks to pass through */
        }

        /* Ranking Display Styles */
        .ranking-display {
            background: transparent;
            padding: 0;
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .ranking-bar {
            background-color: rgba(0, 0, 0, 0.8);
            border-radius: 25px;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            gap: 15px;
            font-weight: bold;
            color: white;
            min-height: 20px;
            width: 400px; /* Fixed width for consistent overlay */
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
        }

        .ranking-bar.animate {
            animation: slideIn 0.5s ease-out;
        }

        .ranking-bar:hover {
            transform: translateX(5px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
        }

        @keyframes slideIn {
            from {
                transform: translateX(-100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .like-update {
            animation: pulse 0.3s ease-in-out;
        }

        .rank-profile {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            font-weight: bold;
            flex-shrink: 0;
            overflow: hidden;
            border: 2px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        }

        .rank-profile img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
        }

        .rank-profile.first {
            border-color: #ffd700;
            box-shadow: 0 2px 8px rgba(255, 215, 0, 0.5);
        }

        .rank-profile.second {
            border-color: #c0c0c0;
            box-shadow: 0 2px 8px rgba(192, 192, 192, 0.5);
        }

        .rank-profile.third {
            border-color: #cd7f32;
            box-shadow: 0 2px 8px rgba(205, 127, 50, 0.5);
        }

        .rank-profile.fallback {
            background: linear-gradient(135deg, #ff4058, #ff6b7a);
            color: white;
        }

        .rank-profile.fallback.first {
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            color: #333;
        }

        .rank-profile.fallback.second {
            background: linear-gradient(135deg, #c0c0c0, #e8e8e8);
            color: #333;
        }

        .rank-profile.fallback.third {
            background: linear-gradient(135deg, #cd7f32, #daa520);
            color: white;
        }

        .user-name-display {
            flex: 1;
            text-align: left;
            font-size: 20px;
            font-weight: 600;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
        }

        .like-count {
            background-color: rgba(255, 255, 255, 0.15);
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 14px;
            font-weight: bold;
            backdrop-filter: blur(3px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* Connection status indicator */
        .connection-status {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.7);
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            color: white;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .status-dot {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 5px;
        }

        .status-connected { background-color: #10b981; }
        .status-disconnected { background-color: #ef4444; }
        .status-connecting { background-color: #f59e0b; }

        /* Hide scrollbars */
        ::-webkit-scrollbar {
            display: none;
        }
    </style>
</head>
<body>
    <div id="app" class="overlay-container">
        <!-- Visual Ranking Display -->
        <div class="ranking-display">
            <div
                v-for="(user, index) in sortedUsers.slice(0, 3)"
                :key="user.userId"
                class="ranking-bar animate"
                :style="{ animationDelay: index * 0.1 + 's' }"
                :data-user-id="user.userId"
            >
                <div 
                    class="rank-profile"
                    :class="{
                        'first': index === 0,
                        'second': index === 1,
                        'third': index === 2,
                        'fallback': !user.profilePictureUrl
                    }"
                >
                    <img v-if="user.profilePictureUrl" :src="user.profilePictureUrl" :alt="user.nickname" @error="handleImageError($event, index)">
                    <span v-else>{{ index + 1 }}</span>
                </div>
                <div class="user-name-display">{{ user.nickname }}</div>
                <div class="like-count">({{ user.likes }})</div>
            </div>
            
            <!-- Show placeholder bars if less than 3 users -->
            <div 
                v-for="i in Math.max(0, 3 - sortedUsers.length)" 
                :key="'placeholder-' + i" 
                class="ranking-bar"
                style="opacity: 0.5;"
            >
                <div 
                    class="rank-profile fallback"
                    :class="{
                        'first': sortedUsers.length + i - 1 === 0,
                        'second': sortedUsers.length + i - 1 === 1,
                        'third': sortedUsers.length + i - 1 === 2
                    }"
                >
                    {{ sortedUsers.length + i }}
                </div>
                <div class="user-name-display">Waiting for likes...</div>
                <div class="like-count">(0)</div>
            </div>
        </div>

        <!-- Connection Status -->
        <div class="connection-status">
            <span 
                class="status-dot"
                :class="{
                    'status-connected': status === 'Connected',
                    'status-disconnected': status === 'Disconnected',
                    'status-connecting': status === 'Connecting'
                }"
            ></span>
            {{ status }}
        </div>
    </div>

    <script>
        const { createApp } = Vue;

        const app = createApp({
            data() {
                return {
                    status: 'Connecting',
                    users: [],
                    websocket: null
                };
            },
            computed: {
                sortedUsers() {
                    return [...this.users]
                        .filter(user => user.likes > 0)
                        .sort((a, b) => b.likes - a.likes);
                }
            },
            mounted() {
                this.setupWebSocket();
            },
            methods: {
                handleImageError(event, index) {
                    // Hide broken image and show fallback
                    event.target.style.display = 'none';
                    event.target.parentElement.classList.add('fallback');
                    event.target.parentElement.innerHTML = `<span>${index + 1}</span>`;
                },
                
                handleLikeMessage(data) {
                    if (data.event === 'like' && data.data && data.data.uniqueId && data.data.likeCount) {
                        const userId = data.data.userId || data.data.uniqueId;
                        const nickname = data.data.nickname || data.data.uniqueId;
                        const profilePictureUrl = data.data.profilePictureUrl || null;
                        const likeCount = data.data.likeCount || 1;
                        
                        this.processLike(userId, nickname, profilePictureUrl, likeCount);
                    }
                },
                
                processLike(userId, nickname, profilePictureUrl, likeCount) {
                    // Find existing user or create new one
                    let user = this.users.find(u => u.userId === userId);
                    
                    if (!user) {
                        // Create new user
                        user = {
                            userId: userId,
                            nickname: nickname,
                            profilePictureUrl: profilePictureUrl,
                            likes: 0
                        };
                        this.users.push(user);
                    }
                    
                    // Add likes to user
                    user.likes += likeCount;
                    
                    // Update user info in case it changed
                    user.nickname = nickname;
                    if (profilePictureUrl) {
                        user.profilePictureUrl = profilePictureUrl;
                    }

                    // Trigger visual feedback
                    this.animateLikeUpdate(userId);
                },

                animateLikeUpdate(userId) {
                    // Add pulse animation to the updated user
                    this.$nextTick(() => {
                        const userElements = document.querySelectorAll('.ranking-bar');
                        userElements.forEach(el => {
                            if (el.dataset.userId === userId.toString()) {
                                el.classList.add('like-update');
                                setTimeout(() => {
                                    el.classList.remove('like-update');
                                }, 300);
                            }
                        });
                    });
                },
                
                updateStatus(newStatus) {
                    this.status = newStatus;
                },
                
                setupWebSocket() {
                    this.connect();
                },
                
                connect() {
                    if (this.websocket) return;

                    this.websocket = new WebSocket("ws://localhost:21213/");

                    this.websocket.onopen = () => {
                        this.updateStatus("Connected");
                    };

                    this.websocket.onclose = () => {
                        this.updateStatus("Disconnected");
                        this.websocket = null;
                        setTimeout(() => this.connect(), 1000);
                    };

                    this.websocket.onerror = () => {
                        this.updateStatus("Disconnected");
                        this.websocket = null;
                        setTimeout(() => this.connect(), 1000);
                    };

                    this.websocket.onmessage = (event) => {
                        let parsedData = JSON.parse(event.data);
                        this.handleLikeMessage(parsedData);
                    };
                }
            }
        }).mount('#app');
    </script>
</body>
</html>
