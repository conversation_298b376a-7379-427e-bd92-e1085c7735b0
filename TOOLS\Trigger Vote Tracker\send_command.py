#!/usr/bin/env python3
"""
Send Custom Commands to Trigger Vote Tracker via TikFinity WebSocket
This script connects to the same WebSocket that TikFinity uses and sends custom commands.

Usage:
python send_command.py reset_votes
python send_command.py toggle_followers_only
python send_command.py ping

This script can be called by:
- Stream Deck buttons
- Streamer.bot actions
- AutoHotkey scripts
- OBS scripts
- Any external automation
"""

import asyncio
import websockets
import json
import sys

async def send_command(command):
    """Send a custom command to the vote tracker via TikFinity WebSocket"""
    try:
        # Connect to TikFinity WebSocket (same as vote tracker uses)
        uri = "ws://localhost:21213/"
        async with websockets.connect(uri) as websocket:
            # Send custom command in the format the vote tracker expects
            command_data = {
                "event": "custom_command",
                "command": command,
                "timestamp": asyncio.get_event_loop().time()
            }
            
            await websocket.send(json.dumps(command_data))
            print(f"✅ Command '{command}' sent successfully!")
            
            # Brief wait to ensure delivery
            await asyncio.sleep(0.1)
            
    except websockets.exceptions.ConnectionRefused:
        print("❌ Error: Could not connect to TikFinity WebSocket.")
        print("   Make sure TikFinity is running and WebSocket is available on ws://localhost:21213/")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

def show_help():
    """Show available commands"""
    print("🎮 Trigger Vote Tracker - Command Sender")
    print("")
    print("Available commands:")
    print("  reset_votes           - Reset the top votes")
    print("  toggle_followers_only - Toggle followers-only mode")
    print("  ping                  - Test connection")
    print("")
    print("Usage:")
    print("  python send_command.py <command>")
    print("")
    print("Examples:")
    print("  python send_command.py reset_votes")
    print("  python send_command.py toggle_followers_only")
    print("")
    print("Integration:")
    print("  • Stream Deck: Create 'System > Open' action with this script")
    print("  • Streamer.bot: Use 'Core > Execute Code (C#)' or 'System > Run Program'")
    print("  • AutoHotkey: Use RunWait command")
    print("  • OBS: Create hotkey that runs this script")

if __name__ == "__main__":
    # Check command line arguments
    if len(sys.argv) < 2:
        show_help()
        sys.exit(1)
    
    command = sys.argv[1].lower()
    
    # Validate command
    valid_commands = ["reset_votes", "toggle_followers_only", "ping"]
    if command not in valid_commands:
        print(f"❌ Error: Unknown command '{command}'")
        print(f"Valid commands: {', '.join(valid_commands)}")
        print("")
        show_help()
        sys.exit(1)
    
    print(f"🚀 Sending command: {command}")
    asyncio.run(send_command(command))
