<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vote Tracker Manager</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.prod.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: rgba(15, 15, 18, 1);
            color: white;
        }

        .app-container {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            gap: 10px;
            padding: 10px;
            max-width: 800px;
            margin: 0 auto;
        }

        .panel {
            background-color: rgba(32, 28, 28, 1);
            border-radius: 12px;
            padding: 15px;
        }

        .panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .panel-title {
            font-size: 18px;
            font-weight: bold;
            margin: 0;
        }

        .connection-led {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #666;
            transition: all 0.3s ease;
        }

        .connection-led.connected {
            background-color: #4CAF50;
            box-shadow: 0 0 10px rgba(76, 175, 80, 0.6);
        }

        .connection-led.connecting {
            background-color: #FF9800;
            box-shadow: 0 0 10px rgba(255, 152, 0, 0.6);
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .settings-button {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: #666;
            border: none;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .settings-button:hover {
            background-color: #777;
            transform: rotate(90deg);
        }

        .treeview-container {
            max-height: 300px;
            overflow-y: auto;
            border: 2px solid rgba(64, 64, 64, 1);
            border-radius: 8px;
            padding: 10px;
            background-color: rgba(20, 20, 20, 1);
        }

        .trigger-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px;
            margin-bottom: 5px;
            background-color: rgba(40, 40, 40, 1);
            border-radius: 6px;
            border-left: 4px solid #4CAF50;
        }

        .trigger-info {
            flex: 1;
        }

        .trigger-name {
            font-weight: bold;
            margin-bottom: 2px;
        }

        .trigger-tags {
            font-size: 12px;
            color: #ccc;
        }

        .trigger-votes {
            font-size: 18px;
            font-weight: bold;
            color: #4CAF50;
            margin-right: 10px;
        }

        .button {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            margin: 0 5px;
        }

        .button-primary {
            background-color: #4CAF50;
            color: white;
        }

        .button-primary:hover {
            background-color: #45a049;
        }

        .button-danger {
            background-color: #f44336;
            color: white;
        }

        .button-danger:hover {
            background-color: #da190b;
        }

        .button-secondary {
            background-color: #666;
            color: white;
        }

        .button-secondary:hover {
            background-color: #777;
        }

        .input-group {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }

        .input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #666;
            border-radius: 6px;
            background-color: rgba(40, 40, 40, 1);
            color: white;
        }

        .settings-popup {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: rgba(32, 28, 28, 1);
            border-radius: 12px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
            z-index: 1000;
            min-width: 300px;
        }

        .settings-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 999;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .checkbox {
            margin-right: 10px;
        }

        .keybind-input {
            padding: 10px 16px;
            border: 1px solid #666;
            border-radius: 6px;
            background-color: rgba(40, 40, 40, 1);
            color: white;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }

        .keybind-input:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 0 5px rgba(76, 175, 80, 0.3);
        }

        .keybind-input.recording {
            border-color: #FF9800;
            background-color: rgba(255, 152, 0, 0.1);
            animation: pulse 1s infinite;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="app-container">
            <!-- Connection Status Panel -->
            <div class="panel">
                <div class="panel-header">
                    <h3 class="panel-title">Vote Tracker Manager</h3>
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <div class="connection-led" :class="connectionClass"></div>
                        <button class="settings-button" @click="showSettings = true" title="Settings">
                            ⚙️
                        </button>
                    </div>
                </div>
                <p>Status: {{ connectionStatus }}</p>
                <p>Overlay: Use "Vote Tracker Overlay.html" in OBS Browser Source</p>
            </div>

            <!-- Trigger Management Panel -->
            <div class="panel">
                <div class="panel-header">
                    <h3 class="panel-title">Trigger Management</h3>
                    <button class="button button-danger" @click="resetTopTrigger">
                        Reset Top Votes
                    </button>
                </div>

                <!-- Add New Trigger -->
                <div class="input-group">
                    <input v-model="newTriggerName" 
                           class="input" 
                           placeholder="Trigger name" 
                           @keyup.enter="addTrigger">
                    <input v-model="newTriggerTags" 
                           class="input" 
                           placeholder="Tags (comma separated)" 
                           @keyup.enter="addTrigger">
                    <button class="button button-primary" @click="addTrigger">Add Trigger</button>
                </div>

                <!-- Trigger List -->
                <div class="treeview-container">
                    <div v-for="trigger in triggers" :key="trigger.id" class="trigger-item">
                        <div class="trigger-info">
                            <div class="trigger-name">{{ trigger.name }}</div>
                            <div class="trigger-tags">{{ trigger.tags.join(', ') }}</div>
                        </div>
                        <div class="trigger-votes">{{ trigger.votes }}</div>
                        <button class="button button-secondary" @click="editTrigger(trigger)">Edit</button>
                        <button class="button button-danger" @click="deleteTrigger(trigger.id)">Delete</button>
                    </div>
                    <div v-if="triggers.length === 0" style="text-align: center; color: #666; padding: 20px;">
                        No triggers configured. Add one above.
                    </div>
                </div>
            </div>
        </div>

        <!-- Settings Popup -->
        <div v-if="showSettings" class="settings-overlay" @click="showSettings = false"></div>
        <div v-if="showSettings" class="settings-popup">
            <h3>Settings</h3>
            
            <div class="checkbox-group">
                <input type="checkbox" 
                       id="followersOnly" 
                       v-model="followersOnly" 
                       class="checkbox"
                       @change="saveSettings">
                <label for="followersOnly">Followers only</label>
            </div>

            <div style="margin-bottom: 15px;">
                <label>Reset Votes Keybind:</label>
                <input type="text" 
                       v-model="keybindDisplay"
                       class="keybind-input"
                       :class="{ recording: recordingKeybind }"
                       :placeholder="recordingKeybind ? 'Press keys...' : 'Click to set keybind'"
                       @click="startKeybindRecording"
                       @keydown="recordKeybind"
                       @blur="stopKeybindRecording"
                       readonly>
            </div>

            <div style="text-align: right;">
                <button class="button button-secondary" @click="showSettings = false">Close</button>
            </div>
        </div>
    </div>

    <script>
        const { createApp } = Vue;

        createApp({
            data() {
                return {
                    triggers: [],
                    websocket: null,
                    connectionStatus: "Connecting...",
                    newTriggerName: '',
                    newTriggerTags: '',
                    userVotes: {},
                    followersOnly: false,
                    showSettings: false,
                    keybind: '',
                    keybindDisplay: '',
                    recordingKeybind: false
                };
            },
            computed: {
                connectionClass() {
                    if (this.connectionStatus === "Connected") return "connected";
                    if (this.connectionStatus === "Connecting...") return "connecting";
                    return "";
                }
            },
            methods: {
                setupWebSocket() {
                    const connect = () => {
                        try {
                            this.websocket = new WebSocket('ws://localhost:21213/');
                            
                            this.websocket.onopen = () => {
                                this.connectionStatus = "Connected";
                                this.broadcastTriggerData();
                            };
                            
                            this.websocket.onclose = () => {
                                this.connectionStatus = "Disconnected";
                                this.websocket = null;
                                setTimeout(connect, 1000);
                            };
                            
                            this.websocket.onerror = () => {
                                this.connectionStatus = "Connection Failed";
                                this.websocket = null;
                                setTimeout(connect, 1000);
                            };

                            this.websocket.onmessage = (event) => {
                                let parsedData = JSON.parse(event.data);
                                this.handleChatMessage(parsedData);
                            };
                        } catch (error) {
                            this.connectionStatus = "Connection Failed";
                            setTimeout(connect, 1000);
                        }
                    };
                    
                    connect();
                },

                broadcastTriggerData() {
                    // Send current trigger data to overlay
                    if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
                        const data = {
                            event: 'trigger_data_update',
                            triggers: this.triggers,
                            followersOnly: this.followersOnly
                        };
                        this.websocket.send(JSON.stringify(data));
                    }
                },

                handleChatMessage(data) {
                    if (data.event === 'chat' && data.data && data.data.comment && data.data.uniqueId) {
                        const message = data.data.comment.toLowerCase().trim();
                        const userId = data.data.uniqueId;
                        const followRole = data.data.followRole || 0;

                        // Check followers-only setting
                        if (this.followersOnly && followRole === 0) {
                            return;
                        }

                        // Check if message matches any trigger tags
                        for (const trigger of this.triggers) {
                            for (const tag of trigger.tags) {
                                if (this.isExactWordMatch(message, tag)) {
                                    this.processVote(userId, trigger.id);
                                    return;
                                }
                            }
                        }
                    }
                },

                isExactWordMatch(message, tag) {
                    const lowerMessage = message.toLowerCase();
                    const lowerTag = tag.toLowerCase();
                    const wordPattern = new RegExp(`\\b${this.escapeRegex(lowerTag)}\\b`);
                    return wordPattern.test(lowerMessage);
                },

                escapeRegex(string) {
                    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                },

                processVote(userId, triggerId) {
                    // Prevent multiple votes from same user
                    if (this.userVotes[userId]) {
                        return;
                    }

                    this.userVotes[userId] = triggerId;
                    
                    const trigger = this.triggers.find(t => t.id === triggerId);
                    if (trigger) {
                        trigger.votes++;
                        this.saveTriggers();
                        this.broadcastTriggerData();
                    }
                },

                addTrigger() {
                    if (!this.newTriggerName.trim() || !this.newTriggerTags.trim()) return;

                    const trigger = {
                        id: Date.now().toString(),
                        name: this.newTriggerName.trim(),
                        tags: this.newTriggerTags.split(',').map(tag => tag.trim()).filter(tag => tag),
                        votes: 0
                    };

                    this.triggers.push(trigger);
                    this.newTriggerName = '';
                    this.newTriggerTags = '';
                    this.saveTriggers();
                    this.broadcastTriggerData();
                },

                deleteTrigger(id) {
                    this.triggers = this.triggers.filter(t => t.id !== id);
                    this.saveTriggers();
                    this.broadcastTriggerData();
                },

                resetTopTrigger() {
                    this.triggers.forEach(trigger => {
                        trigger.votes = 0;
                    });
                    this.userVotes = {};
                    this.saveTriggers();
                    
                    // Broadcast reset to overlay
                    if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
                        this.websocket.send(JSON.stringify({ event: 'votes_reset' }));
                    }
                    this.broadcastTriggerData();
                },

                saveTriggers() {
                    localStorage.setItem('voteTrackerTriggers', JSON.stringify(this.triggers));
                },

                loadTriggers() {
                    const saved = localStorage.getItem('voteTrackerTriggers');
                    if (saved) {
                        this.triggers = JSON.parse(saved);
                    }
                },

                saveSettings() {
                    const settings = {
                        followersOnly: this.followersOnly,
                        keybind: this.keybind
                    };
                    localStorage.setItem('voteTrackerSettings', JSON.stringify(settings));
                    
                    // Broadcast settings to overlay
                    if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
                        this.websocket.send(JSON.stringify({ 
                            event: 'settings_update', 
                            followersOnly: this.followersOnly 
                        }));
                    }
                },

                loadSettings() {
                    const saved = localStorage.getItem('voteTrackerSettings');
                    if (saved) {
                        const settings = JSON.parse(saved);
                        this.followersOnly = settings.followersOnly || false;
                        this.keybind = settings.keybind || '';
                        this.keybindDisplay = this.keybind;
                    }
                },

                startKeybindRecording() {
                    this.recordingKeybind = true;
                    this.keybindDisplay = '';
                },

                stopKeybindRecording() {
                    this.recordingKeybind = false;
                    if (!this.keybindDisplay) {
                        this.keybindDisplay = this.keybind;
                    }
                },

                recordKeybind(event) {
                    if (!this.recordingKeybind) return;
                    event.preventDefault();
                    
                    const keys = [];
                    if (event.ctrlKey) keys.push('Ctrl');
                    if (event.altKey) keys.push('Alt');
                    if (event.shiftKey) keys.push('Shift');
                    if (event.metaKey) keys.push('Meta');
                    
                    let mainKey = this.normalizeKey(event.key, event.code);
                    if (mainKey && !['Control', 'Alt', 'Shift', 'Meta'].includes(event.key)) {
                        keys.push(mainKey);
                    }
                    
                    if (keys.length >= 2 || (keys.length === 1 && this.isSpecialKey(mainKey))) {
                        this.keybind = keys.join('+');
                        this.keybindDisplay = this.keybind;
                        this.recordingKeybind = false;
                        this.saveSettings();
                        event.target.blur();
                    }
                },

                normalizeKey(key, code) {
                    if (code && code.startsWith('Numpad')) {
                        return code;
                    }
                    const keyMap = {
                        ' ': 'Space',
                        'ArrowUp': 'Up',
                        'ArrowDown': 'Down',
                        'ArrowLeft': 'Left',
                        'ArrowRight': 'Right',
                        'Escape': 'Esc',
                        'Delete': 'Del',
                        'Insert': 'Ins'
                    };
                    if (keyMap[key]) return keyMap[key];
                    if (key.length === 1) return key.toUpperCase();
                    return key;
                },

                isSpecialKey(key) {
                    const specialKeys = ['F1', 'F2', 'F3', 'F4', 'F5', 'F6', 'F7', 'F8', 'F9', 'F10', 'F11', 'F12', 'Space', 'Tab', 'Enter', 'Esc', 'Up', 'Down', 'Left', 'Right'];
                    return specialKeys.includes(key) || (key && key.startsWith('Numpad'));
                },

                setupGlobalKeybind() {
                    document.addEventListener('keydown', (event) => {
                        if (!this.keybind) return;
                        
                        if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
                            return;
                        }

                        const pressedKeys = [];
                        if (event.ctrlKey) pressedKeys.push('Ctrl');
                        if (event.altKey) pressedKeys.push('Alt');
                        if (event.shiftKey) pressedKeys.push('Shift');
                        if (event.metaKey) pressedKeys.push('Meta');

                        let mainKey = this.normalizeKey(event.key, event.code);
                        if (mainKey && !['Control', 'Alt', 'Shift', 'Meta'].includes(event.key)) {
                            pressedKeys.push(mainKey);
                        }

                        const pressedKeybind = pressedKeys.join('+');

                        if (pressedKeybind === this.keybind) {
                            event.preventDefault();
                            console.log('Keybind triggered:', pressedKeybind);
                            this.resetTopTrigger();
                        }
                    });
                }
            },
            mounted() {
                this.setupWebSocket();
                this.loadTriggers();
                this.loadSettings();
                this.setupGlobalKeybind();
            }
        }).mount('#app');
    </script>
</body>
</html>
