<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trigger Vote Tracker - OBS Overlay</title>

    <!-- Vue.js CDN -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.prod.js"></script>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: transparent;
            overflow: hidden;
            width: 100vw;
            height: 100vh;
        }

        .overlay-container {
            position: absolute;
            top: 20px;
            left: 20px;
            width: auto;
            height: auto;
            pointer-events: none; /* Allow clicks to pass through */
        }

        /* Ranking Display Styles */
        .ranking-display {
            background: transparent;
            padding: 0;
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .ranking-bar {
            background-color: rgba(0, 0, 0, 0.8);
            border-radius: 25px;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            gap: 15px;
            font-weight: bold;
            color: white;
            min-height: 20px;
            width: 400px; /* Fixed width for consistent overlay */
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
        }

        .ranking-bar.animate {
            animation: slideIn 0.5s ease-out;
        }

        .ranking-bar:hover {
            transform: translateX(5px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
        }

        @keyframes slideIn {
            from {
                transform: translateX(-100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .vote-update {
            animation: pulse 0.3s ease-in-out;
        }

        .rank-number {
            background: linear-gradient(135deg, #ff4058, #ff6b7a);
            color: white;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            font-weight: bold;
            flex-shrink: 0;
            box-shadow: 0 2px 8px rgba(255, 64, 88, 0.3);
        }

        .rank-number.first {
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            color: #333;
        }

        .rank-number.second {
            background: linear-gradient(135deg, #c0c0c0, #e8e8e8);
            color: #333;
        }

        .rank-number.third {
            background: linear-gradient(135deg, #cd7f32, #daa520);
            color: white;
        }

        .trigger-name-display {
            flex: 1;
            text-align: left;
            font-size: 20px;
            font-weight: 600;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
        }

        .vote-count {
            background-color: rgba(255, 255, 255, 0.15);
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 14px;
            font-weight: bold;
            backdrop-filter: blur(3px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* Connection status indicator */
        .connection-status {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.7);
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            color: white;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .status-dot {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 5px;
        }

        .status-connected { background-color: #10b981; }
        .status-disconnected { background-color: #ef4444; }
        .status-connecting { background-color: #f59e0b; }

        /* Hide scrollbars */
        ::-webkit-scrollbar {
            display: none;
        }
    </style>
</head>
<body>
    <div id="app" class="overlay-container">
        <!-- Visual Ranking Display -->
        <div class="ranking-display">
            <div
                v-for="(trigger, index) in sortedTriggers.slice(0, 3)"
                :key="trigger.id"
                class="ranking-bar animate"
                :style="{ animationDelay: index * 0.1 + 's' }"
                :data-trigger-id="trigger.id"
            >
                <div 
                    class="rank-number"
                    :class="{
                        'first': index === 0,
                        'second': index === 1,
                        'third': index === 2
                    }"
                >
                    {{ index + 1 }}
                </div>
                <div class="trigger-name-display">{{ trigger.name }}</div>
                <div class="vote-count">({{ trigger.votes }})</div>
            </div>
            
            <!-- Show placeholder bars if less than 3 triggers -->
            <div 
                v-for="i in Math.max(0, 3 - sortedTriggers.length)" 
                :key="'placeholder-' + i" 
                class="ranking-bar"
                style="opacity: 0.5;"
            >
                <div 
                    class="rank-number"
                    :class="{
                        'first': sortedTriggers.length + i - 1 === 0,
                        'second': sortedTriggers.length + i - 1 === 1,
                        'third': sortedTriggers.length + i - 1 === 2
                    }"
                >
                    {{ sortedTriggers.length + i }}
                </div>
                <div class="trigger-name-display">Waiting for triggers...</div>
                <div class="vote-count">(0)</div>
            </div>
        </div>

        <!-- Connection Status -->
        <div class="connection-status">
            <span 
                class="status-dot"
                :class="{
                    'status-connected': status === 'Connected',
                    'status-disconnected': status === 'Disconnected',
                    'status-connecting': status === 'Connecting'
                }"
            ></span>
            {{ status }}
        </div>
    </div>

    <script>
        // TRIGGER DEFINITIONS - Sync this with your main tool
        const TRIGGER_DEFINITIONS = [
            // Add your triggers here or they will be loaded from localStorage
            // Example: { id: 1, name: "Whisper ASMR", tags: ["whisper", "soft", "gentle"] }
        ];

        const { createApp } = Vue;

        const app = createApp({
            data() {
                return {
                    status: 'Connecting',
                    triggers: [],
                    userVotes: {},
                    websocket: null
                };
            },
            computed: {
                sortedTriggers() {
                    return [...this.triggers].sort((a, b) => b.votes - a.votes);
                }
            },
            mounted() {
                this.loadTriggers();
                this.setupWebSocket();
            },
            methods: {
                loadTriggers() {
                    // Try to load from localStorage first (synced from main tool)
                    const saved = localStorage.getItem('triggerVoteTracker_definitions');
                    let triggerDefinitions = [];
                    
                    if (saved) {
                        triggerDefinitions = JSON.parse(saved);
                    } else if (TRIGGER_DEFINITIONS.length > 0) {
                        triggerDefinitions = TRIGGER_DEFINITIONS;
                    }
                    
                    this.triggers = triggerDefinitions.map(def => ({
                        ...def,
                        votes: 0
                    }));
                    
                    this.userVotes = {};
                },
                
                handleChatMessage(data) {
                    if (data.event === 'chat' && data.data && data.data.comment && data.data.uniqueId) {
                        const message = data.data.comment.toLowerCase().trim();
                        const userId = data.data.uniqueId;
                        
                        // Check if message matches any trigger tags
                        for (const trigger of this.triggers) {
                            for (const tag of trigger.tags) {
                                if (message.includes(tag)) {
                                    this.processVote(userId, trigger.id);
                                    return;
                                }
                            }
                        }
                    }
                },
                
                processVote(userId, triggerId) {
                    // Remove previous vote if exists
                    if (this.userVotes[userId]) {
                        const previousTriggerId = this.userVotes[userId];
                        const previousTrigger = this.triggers.find(t => t.id === previousTriggerId);
                        if (previousTrigger && previousTrigger.votes > 0) {
                            previousTrigger.votes--;
                        }
                    }

                    // Add new vote
                    this.userVotes[userId] = triggerId;
                    const trigger = this.triggers.find(t => t.id === triggerId);
                    if (trigger) {
                        trigger.votes++;
                        // Trigger visual feedback
                        this.animateVoteUpdate(triggerId);
                    }
                },

                animateVoteUpdate(triggerId) {
                    // Add pulse animation to the updated trigger
                    this.$nextTick(() => {
                        const triggerElements = document.querySelectorAll('.ranking-bar');
                        triggerElements.forEach(el => {
                            if (el.dataset.triggerId === triggerId.toString()) {
                                el.classList.add('vote-update');
                                setTimeout(() => {
                                    el.classList.remove('vote-update');
                                }, 300);
                            }
                        });
                    });
                },
                
                updateStatus(newStatus) {
                    this.status = newStatus;
                },
                
                setupWebSocket() {
                    this.connect();
                },
                
                connect() {
                    if (this.websocket) return;

                    this.websocket = new WebSocket("ws://localhost:21213/");

                    this.websocket.onopen = () => {
                        this.updateStatus("Connected");
                    };

                    this.websocket.onclose = () => {
                        this.updateStatus("Disconnected");
                        this.websocket = null;
                        setTimeout(() => this.connect(), 1000);
                    };

                    this.websocket.onerror = () => {
                        this.updateStatus("Disconnected");
                        this.websocket = null;
                        setTimeout(() => this.connect(), 1000);
                    };

                    this.websocket.onmessage = (event) => {
                        let parsedData = JSON.parse(event.data);
                        this.handleChatMessage(parsedData);
                    };
                }
            }
        }).mount('#app');
    </script>
</body>
</html>
