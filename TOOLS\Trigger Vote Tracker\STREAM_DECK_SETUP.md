# Stream Deck & Streamer.bot Setup for Trigger Vote Tracker

This guide shows how to control the Trigger Vote Tracker using Stream Deck buttons and Streamer.bot actions via the existing TikFinity WebSocket connection.

## 🎯 How It Works

The vote tracker now listens for custom commands on the same WebSocket that TikFinity uses (`ws://localhost:21213/`). This means:

- ✅ **No additional servers needed** - Uses existing TikFinity connection
- ✅ **Works with OBS browser sources** - No focus required
- ✅ **Stream Deck compatible** - Simple button actions
- ✅ **Streamer.bot ready** - Easy integration

## 📋 Available Commands

| Command | Description |
|---------|-------------|
| `reset_votes` | Reset the top votes (same as the reset button) |
| `toggle_followers_only` | Toggle followers-only mode on/off |
| `ping` | Test connection (for debugging) |

## 🎮 Stream Deck Setup

### Method 1: System > Open (Recommended)

1. **Add new button** to your Stream Deck
2. **Choose action**: `System > Open`
3. **App/File**: Browse to `send_command.py` in your vote tracker folder
4. **Arguments**: Add the command you want (e.g., `reset_votes`)
5. **Title**: "Reset Votes" or whatever you prefer
6. **Icon**: Choose a reset/refresh icon

**Example Setup:**
```
Action: System > Open
App/File: C:\path\to\TOOLS\Trigger Vote Tracker\send_command.py
Arguments: reset_votes
Title: Reset Votes
```

### Method 2: System > Website

1. **Add new button** to your Stream Deck  
2. **Choose action**: `System > Website`
3. **URL**: `file:///C:/path/to/run_command.html` (create simple HTML file)
4. This method requires creating a small HTML file that runs the Python script

## 🤖 Streamer.bot Setup

### Method 1: Execute Code (C#)

1. **Create new action** in Streamer.bot
2. **Add sub-action**: `Core > Execute Code (C#)`
3. **Code**:
```csharp
using System.Diagnostics;

public class CPHInline
{
    public bool Execute()
    {
        string scriptPath = @"C:\path\to\TOOLS\Trigger Vote Tracker\send_command.py";
        string command = "reset_votes";
        
        ProcessStartInfo startInfo = new ProcessStartInfo
        {
            FileName = "python",
            Arguments = $"\"{scriptPath}\" {command}",
            UseShellExecute = false,
            CreateNoWindow = true
        };
        
        Process.Start(startInfo);
        return true;
    }
}
```

### Method 2: Run Program

1. **Create new action** in Streamer.bot
2. **Add sub-action**: `System > Run Program`
3. **Program**: `python`
4. **Arguments**: `"C:\path\to\send_command.py" reset_votes`
5. **Working Directory**: `C:\path\to\TOOLS\Trigger Vote Tracker\`

## 🔧 Setup Instructions

### Step 1: Install Python Dependencies
```bash
pip install websockets
```

### Step 2: Test the Script
```bash
cd "TOOLS/Trigger Vote Tracker"
python send_command.py reset_votes
```

You should see: `✅ Command 'reset_votes' sent successfully!`

### Step 3: Configure Your Tools

Choose your preferred method:

#### For Stream Deck:
- Use the "System > Open" method above
- Create buttons for each command you want

#### For Streamer.bot:
- Use the "Execute Code (C#)" method above
- Bind to hotkeys, channel point rewards, etc.

#### For AutoHotkey:
```autohotkey
!+Numpad3::
    RunWait, python "C:\path\to\send_command.py" reset_votes, , Hide
return
```

## 🎯 Command Examples

### Reset Votes Button
```bash
python send_command.py reset_votes
```

### Toggle Followers-Only Mode
```bash
python send_command.py toggle_followers_only
```

### Test Connection
```bash
python send_command.py ping
```

## 🐛 Troubleshooting

### "Could not connect to TikFinity WebSocket"
- Make sure TikFinity is running
- Check that the vote tracker is loaded and connected
- Verify WebSocket is available on `ws://localhost:21213/`

### "Command not working"
- Check the vote tracker browser console for command messages
- Test with `python send_command.py ping` first
- Make sure Python and websockets are installed

### Stream Deck button not working
- Verify the path to `send_command.py` is correct
- Test the command manually in command prompt first
- Check that Python is in your system PATH

## 🚀 Advanced Usage

### Multiple Commands in One Button
Create a batch file that runs multiple commands:

```batch
@echo off
python "C:\path\to\send_command.py" reset_votes
timeout /t 1 /nobreak >nul
python "C:\path\to\send_command.py" toggle_followers_only
```

### Streamer.bot Integration with Channel Points
1. Create Streamer.bot action with the C# code above
2. Set trigger to "Channel Point Reward Redemption"
3. Viewers can spend channel points to reset votes!

### OBS Hotkey Integration
1. Add OBS hotkey in Settings > Hotkeys
2. Set action to "Run Program"
3. Program: `python "C:\path\to\send_command.py" reset_votes`
